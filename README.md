# PROGRES WebTU - نظام إدارة الجامعة الإلكتروني

## نظرة عامة
PROGRES WebTU هو نظام إدارة شامل للأنشطة الأكاديمية والإدارية في مؤسسات التعليم العالي. يوفر النظام واجهة ويب سهلة الاستخدام تمكن المستخدمين المختلفين (الطلاب، الأساتذة، الموظفين الإداريين، ومديري النظام) من الوصول إلى خدماتهم المختلفة.

## الميزات الرئيسية

### للطلاب
- التسجيل الإلكتروني
- عرض الجدول الدراسي (محاضرات وامتحانات)
- عرض النتائج وكشف النقاط
- عرض المجموعات والأقسام
- طلب الشهادات والوثائق الرسمية
- طلب الإجازة الأكاديمية
- متابعة الديون والرسوم الدراسية
- عرض وطلب بطاقة الطالب
- خدمات إضافية (التواصل مع الإدارة)

### للأساتذة
- تسجيل الدخول ومراجعة الإشعارات
- رفع وإدارة الدرجات
- معالجة طلبات الاستدراك
- عرض الجدول الدراسي
- التواصل مع الطلاب

### للموظفين الإداريين
- إدارة بيانات الطلاب (إضافة/تعديل/حذف)
- إدارة المجموعات والأقسام
- إدارة كشوف النقاط
- إصدار الشهادات والوثائق الرسمية
- معالجة طلبات الطلاب
- إدارة الغيابات والاستبعادات

### لمديري النظام
- إدارة حسابات المستخدمين
- صيانة النظام وتحديثاته
- معالجة المشاكل التقنية
- مراقبة أداء المنصة
- تحديد صلاحيات المستخدمين

## البنية التقنية

### الواجهة الخلفية (Backend)
- **اللغة**: PHP
- **قاعدة البيانات**: MySQL
- **البنية**: RESTful API
- **الأمان**: CORS headers مُفعلة

### الواجهة الأمامية (Frontend)
- **التقنيات**: HTML5, CSS3, JavaScript (ES6+)
- **البنية**: Model-View-Controller (MVC)
- **التصميم**: Responsive Design
- **الخطوط**: Cairo (دعم اللغة العربية)

## هيكل المشروع

```
progres-webtu/
├── backend/
│   ├── database.sql
│   └── server/
│       ├── config/
│       │   └── database.php
│       ├── studentApi.php
│       ├── teacherApi.php
│       ├── agentApi.php
│       └── adminApi.php
├── frontend/
│   ├── model/
│   │   ├── User.js
│   │   └── Academic.js
│   ├── controller/
│   │   ├── apiConfig.js
│   │   ├── studentController.js
│   │   ├── teacherController.js
│   │   ├── agentController.js
│   │   └── adminController.js
│   └── view/
│       ├── common/
│       │   ├── index.html
│       │   ├── styles.css
│       │   └── script.js
│       └── student/
│           ├── register.html
│           ├── dashboard.html
│           ├── student-styles.css
│           ├── register.js
│           └── dashboard.js
└── project_description/
    ├── description.md
    ├── architecture.md
    ├── relationnal_model.md
    └── use_cases.md
```

## التثبيت والإعداد

### متطلبات النظام
- خادم ويب (Apache/Nginx)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- متصفح ويب حديث

### خطوات التثبيت

1. **إعداد قاعدة البيانات**
   ```sql
   -- تشغيل ملف database.sql في MySQL
   mysql -u root -p < backend/database.sql
   ```

2. **تكوين الاتصال بقاعدة البيانات**
   ```php
   // تعديل ملف backend/server/config/database.php
   private $host = "localhost";
   private $db_name = "progres_webtu";
   private $username = "root";
   private $password = "your_password";
   ```

3. **تكوين API**
   ```javascript
   // تعديل ملف frontend/controller/apiConfig.js
   const API_CONFIG = {
       baseUrl: 'http://your-domain.com/progres-webtu/backend/server',
       // ...
   };
   ```

4. **رفع الملفات**
   - رفع مجلد المشروع إلى خادم الويب
   - التأكد من صلاحيات القراءة والكتابة

## الاستخدام

### تسجيل الدخول الافتراضي
- **المدير**: admin / admin123
- **أستاذ**: jean.dupont / password123
- **طالب**: marie.martin / password123
- **موظف إداري**: ahmed.benali / password123

### الوصول للنظام
1. افتح المتصفح وانتقل إلى: `http://your-domain.com/progres-webtu/frontend/view/common/index.html`
2. اختر نوع المستخدم وأدخل بيانات الدخول
3. ستتم إعادة توجيهك إلى لوحة التحكم المناسبة

## قاعدة البيانات

### الجداول الرئيسية
- **Utilisateur**: بيانات المستخدمين الأساسية
- **Compte**: حسابات تسجيل الدخول
- **Etudiant**: بيانات الطلاب
- **Enseignant**: بيانات الأساتذة
- **Agent**: بيانات الموظفين الإداريين
- **Emploi_du_temps**: الجداول الدراسية
- **Note**: الدرجات والنتائج
- **Module**: المواد الدراسية

## الأمان

### إجراءات الأمان المطبقة
- CORS headers للحماية من الطلبات غير المصرح بها
- تشفير كلمات المرور (يُنصح بتطبيق hashing)
- التحقق من صحة البيانات المدخلة
- إدارة الجلسات عبر localStorage

### توصيات أمنية إضافية
- تطبيق HTTPS في البيئة الإنتاجية
- استخدام prepared statements لمنع SQL injection
- تطبيق rate limiting للحماية من الهجمات
- تشفير كلمات المرور باستخدام bcrypt

## المساهمة

### إرشادات التطوير
1. اتبع معايير الكود المحددة
2. اختبر جميع الوظائف قبل الإرسال
3. وثق أي تغييرات جديدة
4. استخدم التعليقات باللغة العربية أو الإنجليزية

### الإبلاغ عن المشاكل
- استخدم نظام Issues في GitHub
- قدم وصفاً مفصلاً للمشكلة
- أرفق لقطات شاشة إن أمكن

## الترخيص
هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل.

## الدعم
للحصول على الدعم أو طرح الأسئلة:
- افتح issue جديد في GitHub
- راسل فريق التطوير

## الإصدارات المستقبلية
- تطبيق جوال (Android/iOS)
- نظام إشعارات متقدم
- تقارير وإحصائيات مفصلة
- دعم متعدد اللغات
- نظام دفع إلكتروني
