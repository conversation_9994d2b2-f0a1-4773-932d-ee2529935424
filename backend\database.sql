-- PROGRES WebTU Database Schema
-- Academic Management System Database

-- Create database
CREATE DATABASE IF NOT EXISTS progres_webtu;
USE progres_webtu;

-- 1. Utilisateur table
CREATE TABLE Utilisateur (
    ID_utilisateur INT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    date_de_naissance DATE NOT NULL,
    etablissement VARCHAR(200) NOT NULL
);

-- 2. Compte table
CREATE TABLE Compte (
    ID_compte INT PRIMARY KEY AUTO_INCREMENT,
    nom_utilisateur VARCHAR(50) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    ID_utilisateur INT,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE
);

-- 3. Emploi_du_temps table
CREATE TABLE Emploi_du_temps (
    ID_emploi INT PRIMARY KEY AUTO_INCREMENT,
    semestre VARCHAR(50) NOT NULL,
    type ENUM('examen', 'semestre') NOT NULL,
    les_matieres TEXT,
    les_heures VARCHAR(500),
    les_jours VARCHAR(200),
    les_locaux VARCHAR(300)
);

-- 4. Section_et_group table
CREATE TABLE Section_et_group (
    ID_section_group INT PRIMARY KEY AUTO_INCREMENT,
    semestre VARCHAR(50) NOT NULL,
    nom_section VARCHAR(100) NOT NULL,
    num_group INT NOT NULL,
    ID_utilisateur INT,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE SET NULL
);

-- 5. Attestation table
CREATE TABLE Attestation (
    ID_attestation INT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('scolarite', 'inscription') NOT NULL
);

-- 6. Releve_de_note table
CREATE TABLE Releve_de_note (
    ID_RN INT PRIMARY KEY AUTO_INCREMENT,
    les_notes TEXT,
    les_modules TEXT,
    credit VARCHAR(100),
    session ENUM('normale', 'rattrapage') NOT NULL
);

-- 7. Absence_et_exclu table
CREATE TABLE Absence_et_exclu (
    ID_absance INT PRIMARY KEY AUTO_INCREMENT,
    num_absance INT NOT NULL,
    conge_academique VARCHAR(500)
);

-- 8. Note table
CREATE TABLE Note (
    ID_note INT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('examen', 'controle_continu', 'rattrapage') NOT NULL,
    les_matieres TEXT,
    les_coefficient TEXT,
    dette DECIMAL(10,2) DEFAULT 0.00
);

-- 9. Conge_Academique table
CREATE TABLE Conge_Academique (
    ID_CA INT PRIMARY KEY AUTO_INCREMENT,
    ID_utilisateur INT,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE
);

-- 10. Probleme table
CREATE TABLE Probleme (
    ID_prblm INT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(200) NOT NULL,
    contenu TEXT NOT NULL
);

-- 11. Module table
CREATE TABLE Module (
    ID_module INT PRIMARY KEY AUTO_INCREMENT,
    nom_module VARCHAR(200) NOT NULL,
    niveau VARCHAR(100) NOT NULL,
    semestre VARCHAR(50) NOT NULL,
    type VARCHAR(100) NOT NULL
);

-- 12. Enseignant table
CREATE TABLE Enseignant (
    ID_utilisateur INT PRIMARY KEY,
    date_de_recrutement DATE NOT NULL,
    niveau VARCHAR(100) NOT NULL,
    ID_emploi_de_temps INT,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_emploi_de_temps) REFERENCES Emploi_du_temps(ID_emploi) ON DELETE SET NULL
);

-- 13. Étudiant table
CREATE TABLE Etudiant (
    ID_utilisateur INT PRIMARY KEY,
    Annee_bac YEAR NOT NULL,
    cycle VARCHAR(100) NOT NULL,
    annee YEAR NOT NULL,
    diplom VARCHAR(200),
    niveau VARCHAR(100) NOT NULL,
    institu VARCHAR(200),
    departement VARCHAR(200),
    date DATE,
    specialite VARCHAR(200),
    carte VARCHAR(100),
    ID_note INT,
    ID_RN INT,
    ID_attestation INT,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_note) REFERENCES Note(ID_note) ON DELETE SET NULL,
    FOREIGN KEY (ID_RN) REFERENCES Releve_de_note(ID_RN) ON DELETE SET NULL,
    FOREIGN KEY (ID_attestation) REFERENCES Attestation(ID_attestation) ON DELETE SET NULL
);

-- 14. Agent table
CREATE TABLE Agent (
    ID_utilisateur INT PRIMARY KEY,
    date_recrutement DATE NOT NULL,
    niveau VARCHAR(100) NOT NULL,
    ID_emploi INT,
    ID_absence INT,
    ID_CA INT,
    ID_note INT,
    ID_RN INT,
    ID_attestation INT,
    ID_section_group INT,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_emploi) REFERENCES Emploi_du_temps(ID_emploi) ON DELETE SET NULL,
    FOREIGN KEY (ID_absence) REFERENCES Absence_et_exclu(ID_absance) ON DELETE SET NULL,
    FOREIGN KEY (ID_CA) REFERENCES Conge_Academique(ID_CA) ON DELETE SET NULL,
    FOREIGN KEY (ID_note) REFERENCES Note(ID_note) ON DELETE SET NULL,
    FOREIGN KEY (ID_RN) REFERENCES Releve_de_note(ID_RN) ON DELETE SET NULL,
    FOREIGN KEY (ID_attestation) REFERENCES Attestation(ID_attestation) ON DELETE SET NULL,
    FOREIGN KEY (ID_section_group) REFERENCES Section_et_group(ID_section_group) ON DELETE SET NULL
);

-- 15. Admin table
CREATE TABLE Admin (
    ID_compte INT,
    ID_utilisateur INT,
    ID_prblm INT,
    FOREIGN KEY (ID_compte) REFERENCES Compte(ID_compte) ON DELETE CASCADE,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_prblm) REFERENCES Probleme(ID_prblm) ON DELETE SET NULL
);

-- 16. Signale table (Many-to-Many relationship)
CREATE TABLE Signale (
    ID_utilisateur INT,
    ID_prblm INT,
    PRIMARY KEY (ID_utilisateur, ID_prblm),
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_prblm) REFERENCES Probleme(ID_prblm) ON DELETE CASCADE
);

-- 17. Consulter table (Many-to-Many relationship)
CREATE TABLE Consulter (
    ID_utilisateur INT,
    ID_emploi_du_temps INT,
    PRIMARY KEY (ID_utilisateur, ID_emploi_du_temps),
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_emploi_du_temps) REFERENCES Emploi_du_temps(ID_emploi) ON DELETE CASCADE
);

-- 18. Importe table (Many-to-Many relationship)
CREATE TABLE Importe (
    ID_utilisateur INT,
    ID_note INT,
    PRIMARY KEY (ID_utilisateur, ID_note),
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_note) REFERENCES Note(ID_note) ON DELETE CASCADE
);

-- 19. Enseignant_module table
CREATE TABLE Enseignant_module (
    ID_ens_mod INT PRIMARY KEY AUTO_INCREMENT,
    ID_utilisateur INT,
    ID_module INT,
    FOREIGN KEY (ID_utilisateur) REFERENCES Utilisateur(ID_utilisateur) ON DELETE CASCADE,
    FOREIGN KEY (ID_module) REFERENCES Module(ID_module) ON DELETE CASCADE
);

-- Insert sample data for testing
INSERT INTO Utilisateur (nom, prenom, date_de_naissance, etablissement) VALUES
('Admin', 'System', '1980-01-01', 'Université'),
('Dupont', 'Jean', '1990-05-15', 'Université'),
('Martin', 'Marie', '1995-08-20', 'Université'),
('Benali', 'Ahmed', '1985-03-10', 'Université');

INSERT INTO Compte (nom_utilisateur, mot_de_passe, ID_utilisateur) VALUES
('admin', 'admin123', 1),
('jean.dupont', 'password123', 2),
('marie.martin', 'password123', 3),
('ahmed.benali', 'password123', 4);

INSERT INTO Enseignant (ID_utilisateur, date_de_recrutement, niveau) VALUES
(2, '2015-09-01', 'Professeur');

INSERT INTO Etudiant (ID_utilisateur, Annee_bac, cycle, annee, niveau, departement, specialite) VALUES
(3, 2020, 'Licence', 2023, 'L3', 'Informatique', 'Génie Logiciel');

INSERT INTO Agent (ID_utilisateur, date_recrutement, niveau) VALUES
(4, '2018-01-15', 'Agent administratif');

-- Add indexes for better performance
CREATE INDEX idx_compte_username ON Compte(nom_utilisateur);
CREATE INDEX idx_etudiant_niveau ON Etudiant(niveau);
CREATE INDEX idx_etudiant_departement ON Etudiant(departement);
CREATE INDEX idx_enseignant_niveau ON Enseignant(niveau);
CREATE INDEX idx_emploi_semestre ON Emploi_du_temps(semestre);
CREATE INDEX idx_emploi_type ON Emploi_du_temps(type);
CREATE INDEX idx_section_semestre ON Section_et_group(semestre);
CREATE INDEX idx_note_type ON Note(type);
CREATE INDEX idx_releve_session ON Releve_de_note(session);

-- Add some sample data for testing
INSERT INTO Module (nom_module, niveau, semestre, type) VALUES
('Programmation Web', 'L3', 'S5', 'Informatique'),
('Base de Données', 'L3', 'S5', 'Informatique'),
('Réseaux Informatiques', 'L3', 'S6', 'Informatique'),
('Génie Logiciel', 'L3', 'S6', 'Informatique'),
('Mathématiques Discrètes', 'L2', 'S3', 'Mathématiques'),
('Analyse Numérique', 'L2', 'S4', 'Mathématiques');

INSERT INTO Enseignant_module (ID_utilisateur, ID_module) VALUES
(2, 1), (2, 2);

INSERT INTO Emploi_du_temps (semestre, type, les_matieres, les_heures, les_jours, les_locaux) VALUES
('S5', 'semestre', 'Programmation Web, Base de Données', '08:00-10:00, 10:15-12:15', 'Dimanche, Lundi', 'Salle 101, Salle 102'),
('S5', 'examen', 'Programmation Web', '08:00-10:00', 'Dimanche', 'Amphithéâtre A'),
('S6', 'semestre', 'Réseaux, Génie Logiciel', '14:00-16:00, 16:15-18:15', 'Mardi, Mercredi', 'Labo Info, Salle 201');

INSERT INTO Section_et_group (semestre, nom_section, num_group, ID_utilisateur) VALUES
('S5', 'Informatique L3', 1, 3),
('S5', 'Informatique L3', 2, NULL),
('S6', 'Informatique L3', 1, NULL);

INSERT INTO Note (type, les_matieres, les_coefficient, dette) VALUES
('examen', 'Programmation Web: 15/20, Base de Données: 14/20', 'Programmation Web: 3, Base de Données: 2', 0.00),
('controle_continu', 'Programmation Web: 16/20, Base de Données: 13/20', 'Programmation Web: 1, Base de Données: 1', 0.00);

INSERT INTO Releve_de_note (les_notes, les_modules, credit, session) VALUES
('Programmation Web: 15.5/20, Base de Données: 13.5/20, Moyenne: 14.7/20', 'Programmation Web, Base de Données', '30', 'normale');

-- Update student with note and grade report references
UPDATE Etudiant SET ID_note = 1, ID_RN = 1 WHERE ID_utilisateur = 3;

-- Add some problems for testing
INSERT INTO Probleme (nom, contenu) VALUES
('مشكلة في تسجيل الدخول', 'لا أستطيع تسجيل الدخول إلى النظام'),
('خطأ في عرض النتائج', 'النتائج لا تظهر بشكل صحيح'),
('طلب تحديث البيانات', 'أريد تحديث بياناتي الشخصية');

-- Link problems to users
INSERT INTO Signale (ID_utilisateur, ID_prblm) VALUES
(3, 1), (3, 2), (2, 3);
