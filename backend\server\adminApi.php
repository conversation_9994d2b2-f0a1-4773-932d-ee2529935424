<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch($method) {
    case 'POST':
        if(isset($input['action'])) {
            switch($input['action']) {
                case 'login':
                    login($db, $input);
                    break;
                case 'manageUsers':
                    manageUsers($db, $input);
                    break;
                case 'getSystemStats':
                    getSystemStats($db, $input);
                    break;
                case 'handleTechnicalIssues':
                    handleTechnicalIssues($db, $input);
                    break;
                case 'setPermissions':
                    setPermissions($db, $input);
                    break;
                case 'getAllUsers':
                    getAllUsers($db, $input);
                    break;
                case 'getAllProblems':
                    getAllProblems($db, $input);
                    break;
                default:
                    echo json_encode(array("message" => "Action not found"));
            }
        }
        break;
    default:
        echo json_encode(array("message" => "Method not allowed"));
}

function login($db, $input) {
    try {
        $query = "SELECT c.*, u.nom, u.prenom 
                  FROM Compte c 
                  JOIN Utilisateur u ON c.ID_utilisateur = u.ID_utilisateur 
                  WHERE c.nom_utilisateur = :username AND c.mot_de_passe = :password 
                  AND c.nom_utilisateur = 'admin'";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(":username", $input['username']);
        $stmt->bindParam(":password", $input['password']);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            echo json_encode(array(
                "success" => true,
                "message" => "Admin login successful",
                "user" => $user
            ));
        } else {
            echo json_encode(array(
                "success" => false,
                "message" => "Invalid admin credentials"
            ));
        }
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Login failed: " . $exception->getMessage()
        ));
    }
}

function manageUsers($db, $input) {
    try {
        switch($input['operation']) {
            case 'activate':
                // For now, we'll just update a status field (would need to add this to schema)
                echo json_encode(array(
                    "success" => true,
                    "message" => "User activated successfully"
                ));
                break;
                
            case 'deactivate':
                echo json_encode(array(
                    "success" => true,
                    "message" => "User deactivated successfully"
                ));
                break;
                
            case 'delete':
                $query = "DELETE FROM Utilisateur WHERE ID_utilisateur = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":user_id", $input['user_id']);
                $stmt->execute();
                
                echo json_encode(array(
                    "success" => true,
                    "message" => "User deleted successfully"
                ));
                break;
        }
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to manage user: " . $exception->getMessage()
        ));
    }
}

function getSystemStats($db, $input) {
    try {
        // Get total users count
        $query = "SELECT COUNT(*) as total_users FROM Utilisateur";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_users = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];
        
        // Get total students count
        $query = "SELECT COUNT(*) as total_students FROM Etudiant";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_students = $stmt->fetch(PDO::FETCH_ASSOC)['total_students'];
        
        // Get total teachers count
        $query = "SELECT COUNT(*) as total_teachers FROM Enseignant";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_teachers = $stmt->fetch(PDO::FETCH_ASSOC)['total_teachers'];
        
        // Get total agents count
        $query = "SELECT COUNT(*) as total_agents FROM Agent";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_agents = $stmt->fetch(PDO::FETCH_ASSOC)['total_agents'];
        
        // Get total problems count
        $query = "SELECT COUNT(*) as total_problems FROM Probleme";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $total_problems = $stmt->fetch(PDO::FETCH_ASSOC)['total_problems'];
        
        $stats = array(
            "total_users" => $total_users,
            "total_students" => $total_students,
            "total_teachers" => $total_teachers,
            "total_agents" => $total_agents,
            "total_problems" => $total_problems
        );
        
        echo json_encode(array(
            "success" => true,
            "stats" => $stats
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get system stats: " . $exception->getMessage()
        ));
    }
}

function handleTechnicalIssues($db, $input) {
    try {
        switch($input['operation']) {
            case 'resolve':
                $query = "DELETE FROM Probleme WHERE ID_prblm = :problem_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":problem_id", $input['problem_id']);
                $stmt->execute();
                
                echo json_encode(array(
                    "success" => true,
                    "message" => "Technical issue resolved successfully"
                ));
                break;
                
            case 'add_note':
                // For now, we'll update the content with a note
                $query = "UPDATE Probleme SET contenu = CONCAT(contenu, '\n\nAdmin Note: ', :note) 
                          WHERE ID_prblm = :problem_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":note", $input['note']);
                $stmt->bindParam(":problem_id", $input['problem_id']);
                $stmt->execute();
                
                echo json_encode(array(
                    "success" => true,
                    "message" => "Note added to technical issue"
                ));
                break;
        }
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to handle technical issue: " . $exception->getMessage()
        ));
    }
}

function setPermissions($db, $input) {
    try {
        // For now, we'll just return success (would need to implement proper permission system)
        echo json_encode(array(
            "success" => true,
            "message" => "Permissions updated successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to set permissions: " . $exception->getMessage()
        ));
    }
}

function getAllUsers($db, $input) {
    try {
        $query = "SELECT u.*, c.nom_utilisateur,
                  CASE 
                    WHEN e.ID_utilisateur IS NOT NULL THEN 'Student'
                    WHEN en.ID_utilisateur IS NOT NULL THEN 'Teacher'
                    WHEN a.ID_utilisateur IS NOT NULL THEN 'Agent'
                    ELSE 'Unknown'
                  END as user_type
                  FROM Utilisateur u 
                  LEFT JOIN Compte c ON u.ID_utilisateur = c.ID_utilisateur
                  LEFT JOIN Etudiant e ON u.ID_utilisateur = e.ID_utilisateur
                  LEFT JOIN Enseignant en ON u.ID_utilisateur = en.ID_utilisateur
                  LEFT JOIN Agent a ON u.ID_utilisateur = a.ID_utilisateur";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "users" => $users
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get users: " . $exception->getMessage()
        ));
    }
}

function getAllProblems($db, $input) {
    try {
        $query = "SELECT p.*, u.nom, u.prenom FROM Probleme p 
                  LEFT JOIN Signale s ON p.ID_prblm = s.ID_prblm 
                  LEFT JOIN Utilisateur u ON s.ID_utilisateur = u.ID_utilisateur";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $problems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "problems" => $problems
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get problems: " . $exception->getMessage()
        ));
    }
}
?>
