<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch($method) {
    case 'POST':
        if(isset($input['action'])) {
            switch($input['action']) {
                case 'login':
                    login($db, $input);
                    break;
                case 'manageStudent':
                    manageStudent($db, $input);
                    break;
                case 'manageGroups':
                    manageGroups($db, $input);
                    break;
                case 'manageGrades':
                    manageGrades($db, $input);
                    break;
                case 'issueCertificate':
                    issueCertificate($db, $input);
                    break;
                case 'processRequests':
                    processRequests($db, $input);
                    break;
                case 'manageAbsences':
                    manageAbsences($db, $input);
                    break;
                case 'getAllStudents':
                    getAllStudents($db, $input);
                    break;
                default:
                    echo json_encode(array("message" => "Action not found"));
            }
        }
        break;
    default:
        echo json_encode(array("message" => "Method not allowed"));
}

function login($db, $input) {
    try {
        $query = "SELECT c.*, u.nom, u.prenom, a.niveau, a.date_recrutement 
                  FROM Compte c 
                  JOIN Utilisateur u ON c.ID_utilisateur = u.ID_utilisateur 
                  JOIN Agent a ON u.ID_utilisateur = a.ID_utilisateur
                  WHERE c.nom_utilisateur = :username AND c.mot_de_passe = :password";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(":username", $input['username']);
        $stmt->bindParam(":password", $input['password']);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            echo json_encode(array(
                "success" => true,
                "message" => "Login successful",
                "user" => $user
            ));
        } else {
            echo json_encode(array(
                "success" => false,
                "message" => "Invalid credentials"
            ));
        }
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Login failed: " . $exception->getMessage()
        ));
    }
}

function manageStudent($db, $input) {
    try {
        switch($input['operation']) {
            case 'add':
                // Add new student
                $query = "INSERT INTO Utilisateur (nom, prenom, date_de_naissance, etablissement) 
                          VALUES (:nom, :prenom, :date_naissance, :etablissement)";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":nom", $input['nom']);
                $stmt->bindParam(":prenom", $input['prenom']);
                $stmt->bindParam(":date_naissance", $input['date_naissance']);
                $stmt->bindParam(":etablissement", $input['etablissement']);
                $stmt->execute();
                
                $user_id = $db->lastInsertId();
                
                $query = "INSERT INTO Etudiant (ID_utilisateur, Annee_bac, cycle, annee, niveau, departement, specialite) 
                          VALUES (:user_id, :annee_bac, :cycle, :annee, :niveau, :departement, :specialite)";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":user_id", $user_id);
                $stmt->bindParam(":annee_bac", $input['annee_bac']);
                $stmt->bindParam(":cycle", $input['cycle']);
                $stmt->bindParam(":annee", $input['annee']);
                $stmt->bindParam(":niveau", $input['niveau']);
                $stmt->bindParam(":departement", $input['departement']);
                $stmt->bindParam(":specialite", $input['specialite']);
                $stmt->execute();
                break;
                
            case 'update':
                // Update student
                $query = "UPDATE Etudiant SET niveau = :niveau, departement = :departement, 
                          specialite = :specialite WHERE ID_utilisateur = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":niveau", $input['niveau']);
                $stmt->bindParam(":departement", $input['departement']);
                $stmt->bindParam(":specialite", $input['specialite']);
                $stmt->bindParam(":user_id", $input['user_id']);
                $stmt->execute();
                break;
                
            case 'delete':
                // Delete student
                $query = "DELETE FROM Utilisateur WHERE ID_utilisateur = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":user_id", $input['user_id']);
                $stmt->execute();
                break;
        }
        
        echo json_encode(array(
            "success" => true,
            "message" => "Student management operation completed successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to manage student: " . $exception->getMessage()
        ));
    }
}

function manageGroups($db, $input) {
    try {
        switch($input['operation']) {
            case 'add':
                $query = "INSERT INTO Section_et_group (semestre, nom_section, num_group, ID_utilisateur) 
                          VALUES (:semestre, :nom_section, :num_group, :user_id)";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":semestre", $input['semestre']);
                $stmt->bindParam(":nom_section", $input['nom_section']);
                $stmt->bindParam(":num_group", $input['num_group']);
                $stmt->bindParam(":user_id", $input['user_id']);
                $stmt->execute();
                break;
                
            case 'update':
                $query = "UPDATE Section_et_group SET nom_section = :nom_section, 
                          num_group = :num_group WHERE ID_section_group = :group_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":nom_section", $input['nom_section']);
                $stmt->bindParam(":num_group", $input['num_group']);
                $stmt->bindParam(":group_id", $input['group_id']);
                $stmt->execute();
                break;
                
            case 'delete':
                $query = "DELETE FROM Section_et_group WHERE ID_section_group = :group_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":group_id", $input['group_id']);
                $stmt->execute();
                break;
        }
        
        echo json_encode(array(
            "success" => true,
            "message" => "Group management operation completed successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to manage groups: " . $exception->getMessage()
        ));
    }
}

function manageGrades($db, $input) {
    try {
        $query = "INSERT INTO Releve_de_note (les_notes, les_modules, credit, session) 
                  VALUES (:notes, :modules, :credit, :session)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":notes", $input['notes']);
        $stmt->bindParam(":modules", $input['modules']);
        $stmt->bindParam(":credit", $input['credit']);
        $stmt->bindParam(":session", $input['session']);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Grades managed successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to manage grades: " . $exception->getMessage()
        ));
    }
}

function issueCertificate($db, $input) {
    try {
        $query = "INSERT INTO Attestation (type) VALUES (:type)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":type", $input['type']);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Certificate issued successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to issue certificate: " . $exception->getMessage()
        ));
    }
}

function processRequests($db, $input) {
    try {
        // Get all academic leave requests
        $query = "SELECT ca.*, u.nom, u.prenom FROM Conge_Academique ca 
                  JOIN Utilisateur u ON ca.ID_utilisateur = u.ID_utilisateur";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "requests" => $requests
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to process requests: " . $exception->getMessage()
        ));
    }
}

function manageAbsences($db, $input) {
    try {
        $query = "INSERT INTO Absence_et_exclu (num_absance, conge_academique) 
                  VALUES (:num_absance, :conge_academique)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":num_absance", $input['num_absance']);
        $stmt->bindParam(":conge_academique", $input['conge_academique']);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Absence managed successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to manage absence: " . $exception->getMessage()
        ));
    }
}

function getAllStudents($db, $input) {
    try {
        $query = "SELECT u.*, e.* FROM Utilisateur u 
                  JOIN Etudiant e ON u.ID_utilisateur = e.ID_utilisateur";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "students" => $students
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get students: " . $exception->getMessage()
        ));
    }
}
?>
