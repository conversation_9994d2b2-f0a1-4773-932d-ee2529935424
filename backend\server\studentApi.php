<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch($method) {
    case 'POST':
        if(isset($input['action'])) {
            switch($input['action']) {
                case 'login':
                    login($db, $input);
                    break;
                case 'register':
                    register($db, $input);
                    break;
                case 'getSchedule':
                    getSchedule($db, $input);
                    break;
                case 'getGrades':
                    getGrades($db, $input);
                    break;
                case 'getGroups':
                    getGroups($db, $input);
                    break;
                case 'requestCertificate':
                    requestCertificate($db, $input);
                    break;
                case 'requestAcademicLeave':
                    requestAcademicLeave($db, $input);
                    break;
                case 'getDebts':
                    getDebts($db, $input);
                    break;
                case 'getStudentCard':
                    getStudentCard($db, $input);
                    break;
                default:
                    echo json_encode(array("message" => "Action not found"));
            }
        }
        break;
    default:
        echo json_encode(array("message" => "Method not allowed"));
}

function login($db, $input) {
    try {
        $query = "SELECT c.*, u.nom, u.prenom, e.niveau, e.departement, e.specialite 
                  FROM Compte c 
                  JOIN Utilisateur u ON c.ID_utilisateur = u.ID_utilisateur 
                  LEFT JOIN Etudiant e ON u.ID_utilisateur = e.ID_utilisateur
                  WHERE c.nom_utilisateur = :username AND c.mot_de_passe = :password";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(":username", $input['username']);
        $stmt->bindParam(":password", $input['password']);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            echo json_encode(array(
                "success" => true,
                "message" => "Login successful",
                "user" => $user
            ));
        } else {
            echo json_encode(array(
                "success" => false,
                "message" => "Invalid credentials"
            ));
        }
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Login failed: " . $exception->getMessage()
        ));
    }
}

function register($db, $input) {
    try {
        // Insert into Utilisateur table
        $query = "INSERT INTO Utilisateur (nom, prenom, date_de_naissance, etablissement) 
                  VALUES (:nom, :prenom, :date_naissance, :etablissement)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":nom", $input['nom']);
        $stmt->bindParam(":prenom", $input['prenom']);
        $stmt->bindParam(":date_naissance", $input['date_naissance']);
        $stmt->bindParam(":etablissement", $input['etablissement']);
        $stmt->execute();
        
        $user_id = $db->lastInsertId();
        
        // Insert into Compte table
        $query = "INSERT INTO Compte (nom_utilisateur, mot_de_passe, ID_utilisateur) 
                  VALUES (:username, :password, :user_id)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":username", $input['username']);
        $stmt->bindParam(":password", $input['password']);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        // Insert into Etudiant table
        $query = "INSERT INTO Etudiant (ID_utilisateur, Annee_bac, cycle, annee, niveau, departement, specialite) 
                  VALUES (:user_id, :annee_bac, :cycle, :annee, :niveau, :departement, :specialite)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":annee_bac", $input['annee_bac']);
        $stmt->bindParam(":cycle", $input['cycle']);
        $stmt->bindParam(":annee", $input['annee']);
        $stmt->bindParam(":niveau", $input['niveau']);
        $stmt->bindParam(":departement", $input['departement']);
        $stmt->bindParam(":specialite", $input['specialite']);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Registration successful"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Registration failed: " . $exception->getMessage()
        ));
    }
}

function getSchedule($db, $input) {
    try {
        $query = "SELECT * FROM Emploi_du_temps WHERE type = :type";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":type", $input['type']);
        $stmt->execute();
        
        $schedules = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "schedules" => $schedules
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get schedule: " . $exception->getMessage()
        ));
    }
}

function getGrades($db, $input) {
    try {
        $query = "SELECT rn.*, n.* FROM Etudiant e 
                  LEFT JOIN Releve_de_note rn ON e.ID_RN = rn.ID_RN 
                  LEFT JOIN Note n ON e.ID_note = n.ID_note 
                  WHERE e.ID_utilisateur = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $input['user_id']);
        $stmt->execute();
        
        $grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "grades" => $grades
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get grades: " . $exception->getMessage()
        ));
    }
}

function getGroups($db, $input) {
    try {
        $query = "SELECT * FROM Section_et_group ORDER BY nom_section, num_group";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "groups" => $groups
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get groups: " . $exception->getMessage()
        ));
    }
}

function requestCertificate($db, $input) {
    try {
        $query = "INSERT INTO Attestation (type) VALUES (:type)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":type", $input['type']);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Certificate request submitted successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to request certificate: " . $exception->getMessage()
        ));
    }
}

function requestAcademicLeave($db, $input) {
    try {
        $query = "INSERT INTO Conge_Academique (ID_utilisateur) VALUES (:user_id)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $input['user_id']);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Academic leave request submitted successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to request academic leave: " . $exception->getMessage()
        ));
    }
}

function getDebts($db, $input) {
    try {
        $query = "SELECT n.dette FROM Etudiant e 
                  LEFT JOIN Note n ON e.ID_note = n.ID_note 
                  WHERE e.ID_utilisateur = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $input['user_id']);
        $stmt->execute();
        
        $debt = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "debt" => $debt['dette'] ?? 0
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get debts: " . $exception->getMessage()
        ));
    }
}

function getStudentCard($db, $input) {
    try {
        $query = "SELECT carte FROM Etudiant WHERE ID_utilisateur = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $input['user_id']);
        $stmt->execute();
        
        $card = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "card" => $card['carte'] ?? null
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get student card: " . $exception->getMessage()
        ));
    }
}
?>
