<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch($method) {
    case 'POST':
        if(isset($input['action'])) {
            switch($input['action']) {
                case 'login':
                    login($db, $input);
                    break;
                case 'getSchedule':
                    getTeacherSchedule($db, $input);
                    break;
                case 'uploadGrades':
                    uploadGrades($db, $input);
                    break;
                case 'getModules':
                    getTeacherModules($db, $input);
                    break;
                case 'getNotifications':
                    getNotifications($db, $input);
                    break;
                case 'sendMessage':
                    sendMessage($db, $input);
                    break;
                default:
                    echo json_encode(array("message" => "Action not found"));
            }
        }
        break;
    default:
        echo json_encode(array("message" => "Method not allowed"));
}

function login($db, $input) {
    try {
        $query = "SELECT c.*, u.nom, u.prenom, e.niveau, e.date_de_recrutement 
                  FROM Compte c 
                  JOIN Utilisateur u ON c.ID_utilisateur = u.ID_utilisateur 
                  JOIN Enseignant e ON u.ID_utilisateur = e.ID_utilisateur
                  WHERE c.nom_utilisateur = :username AND c.mot_de_passe = :password";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(":username", $input['username']);
        $stmt->bindParam(":password", $input['password']);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            echo json_encode(array(
                "success" => true,
                "message" => "Login successful",
                "user" => $user
            ));
        } else {
            echo json_encode(array(
                "success" => false,
                "message" => "Invalid credentials"
            ));
        }
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Login failed: " . $exception->getMessage()
        ));
    }
}

function getTeacherSchedule($db, $input) {
    try {
        $query = "SELECT et.* FROM Enseignant e 
                  JOIN Emploi_du_temps et ON e.ID_emploi_de_temps = et.ID_emploi 
                  WHERE e.ID_utilisateur = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $input['user_id']);
        $stmt->execute();
        
        $schedule = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "schedule" => $schedule
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get schedule: " . $exception->getMessage()
        ));
    }
}

function uploadGrades($db, $input) {
    try {
        // Insert grades into Note table
        $query = "INSERT INTO Note (type, les_matieres, les_coefficient, dette) 
                  VALUES (:type, :matieres, :coefficients, :dette)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":type", $input['type']);
        $stmt->bindParam(":matieres", $input['matieres']);
        $stmt->bindParam(":coefficients", $input['coefficients']);
        $stmt->bindParam(":dette", $input['dette']);
        $stmt->execute();
        
        $note_id = $db->lastInsertId();
        
        // Link to teacher through Importe table
        $query = "INSERT INTO Importe (ID_utilisateur, ID_note) VALUES (:user_id, :note_id)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $input['user_id']);
        $stmt->bindParam(":note_id", $note_id);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Grades uploaded successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to upload grades: " . $exception->getMessage()
        ));
    }
}

function getTeacherModules($db, $input) {
    try {
        $query = "SELECT m.* FROM Enseignant_module em 
                  JOIN Module m ON em.ID_module = m.ID_module 
                  WHERE em.ID_utilisateur = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":user_id", $input['user_id']);
        $stmt->execute();
        
        $modules = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(array(
            "success" => true,
            "modules" => $modules
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get modules: " . $exception->getMessage()
        ));
    }
}

function getNotifications($db, $input) {
    try {
        // For now, return sample notifications
        $notifications = array(
            array(
                "id" => 1,
                "title" => "New semester schedule available",
                "message" => "The schedule for the new semester has been published",
                "date" => date('Y-m-d H:i:s')
            ),
            array(
                "id" => 2,
                "title" => "Grade submission deadline",
                "message" => "Please submit all grades before the deadline",
                "date" => date('Y-m-d H:i:s')
            )
        );
        
        echo json_encode(array(
            "success" => true,
            "notifications" => $notifications
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to get notifications: " . $exception->getMessage()
        ));
    }
}

function sendMessage($db, $input) {
    try {
        // Insert message into Probleme table (using it as a message system)
        $query = "INSERT INTO Probleme (nom, contenu) VALUES (:title, :message)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(":title", $input['title']);
        $stmt->bindParam(":message", $input['message']);
        $stmt->execute();
        
        echo json_encode(array(
            "success" => true,
            "message" => "Message sent successfully"
        ));
    } catch(PDOException $exception) {
        echo json_encode(array(
            "success" => false,
            "message" => "Failed to send message: " . $exception->getMessage()
        ));
    }
}
?>
