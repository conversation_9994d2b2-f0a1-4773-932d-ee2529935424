# دليل النشر - PROGRES WebTU

## نشر النظام على الخادم

### 1. متطلبات الخادم

#### الحد الأدنى للمتطلبات
- **نظام التشغيل**: Linux (Ubuntu 20.04+ مُفضل) أو Windows Server
- **خادم الويب**: Apache 2.4+ أو Nginx 1.18+
- **PHP**: 7.4+ (8.0+ مُفضل)
- **قاعدة البيانات**: MySQL 5.7+ أو MariaDB 10.3+
- **الذاكرة**: 2GB RAM كحد أدنى، 4GB مُفضل
- **التخزين**: 10GB مساحة فارغة كحد أدنى
- **SSL**: شهادة SSL صالحة

#### PHP Extensions المطلوبة
```bash
php-mysql
php-json
php-mbstring
php-curl
php-xml
php-zip
```

### 2. <PERSON><PERSON><PERSON><PERSON> الخادم

#### أ. تثبيت Apache و PHP (Ubuntu)
```bash
sudo apt update
sudo apt install apache2 php php-mysql php-json php-mbstring php-curl php-xml
sudo systemctl enable apache2
sudo systemctl start apache2
```

#### ب. تثبيت MySQL
```bash
sudo apt install mysql-server
sudo mysql_secure_installation
```

#### ج. إعداد Virtual Host
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/progres-webtu
    
    <Directory /var/www/progres-webtu>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/progres-webtu-error.log
    CustomLog ${APACHE_LOG_DIR}/progres-webtu-access.log combined
</VirtualHost>
```

### 3. نشر الملفات

#### أ. رفع الملفات
```bash
# نسخ ملفات المشروع
sudo cp -r progres-webtu /var/www/
sudo chown -R www-data:www-data /var/www/progres-webtu
sudo chmod -R 755 /var/www/progres-webtu
```

#### ب. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE progres_webtu CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم مخصص
CREATE USER 'progres_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON progres_webtu.* TO 'progres_user'@'localhost';
FLUSH PRIVILEGES;

-- استيراد البيانات
mysql -u progres_user -p progres_webtu < backend/database.sql
```

### 4. تكوين الأمان

#### أ. تحديث إعدادات قاعدة البيانات
```php
// backend/server/config/database.php
private $host = "localhost";
private $db_name = "progres_webtu";
private $username = "progres_user";
private $password = "secure_password_here";
```

#### ب. تحديث إعدادات API
```javascript
// frontend/controller/apiConfig.js
const API_CONFIG = {
    baseUrl: 'https://your-domain.com/backend/server',
    // ...
};
```

#### ج. إعداد SSL
```bash
# تثبيت Certbot للحصول على شهادة مجانية
sudo apt install certbot python3-certbot-apache
sudo certbot --apache -d your-domain.com
```

### 5. تحسينات الأداء

#### أ. تفعيل Compression
```apache
# في .htaccess
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css text/javascript application/javascript application/json
</IfModule>
```

#### ب. تحسين MySQL
```sql
-- في my.cnf
[mysqld]
innodb_buffer_pool_size = 1G
query_cache_size = 256M
query_cache_type = 1
max_connections = 200
```

#### ج. تفعيل Caching
```apache
# في .htaccess
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>
```

### 6. المراقبة والصيانة

#### أ. إعداد Log Monitoring
```bash
# مراقبة logs Apache
sudo tail -f /var/log/apache2/progres-webtu-error.log

# مراقبة logs MySQL
sudo tail -f /var/log/mysql/error.log
```

#### ب. النسخ الاحتياطية
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u progres_user -p progres_webtu > /backups/progres_webtu_$DATE.sql
tar -czf /backups/progres_webtu_files_$DATE.tar.gz /var/www/progres-webtu
```

#### ج. Cron Jobs للصيانة
```bash
# إضافة إلى crontab
# نسخة احتياطية يومية في الساعة 2 صباحاً
0 2 * * * /path/to/backup.sh

# تنظيف logs أسبوعياً
0 3 * * 0 find /var/log/apache2/ -name "*.log" -mtime +7 -delete
```

### 7. اختبار النشر

#### أ. اختبارات الوظائف
```bash
# اختبار الاتصال بقاعدة البيانات
curl -X POST https://your-domain.com/backend/server/studentApi.php \
  -H "Content-Type: application/json" \
  -d '{"action":"login","username":"marie.martin","password":"password123"}'
```

#### ب. اختبارات الأداء
```bash
# اختبار سرعة التحميل
curl -w "@curl-format.txt" -o /dev/null -s https://your-domain.com/

# اختبار الحمولة
ab -n 100 -c 10 https://your-domain.com/
```

### 8. إعدادات الأمان المتقدمة

#### أ. Firewall
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

#### ب. تحديث كلمات المرور الافتراضية
```sql
-- تحديث كلمة مرور المدير
UPDATE Compte SET mot_de_passe = 'new_secure_password' WHERE nom_utilisateur = 'admin';
```

#### ج. إعداد Rate Limiting
```apache
# في .htaccess
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
```

### 9. استكشاف الأخطاء

#### مشاكل شائعة في النشر

##### خطأ 500 Internal Server Error
```bash
# فحص logs
sudo tail -f /var/log/apache2/error.log

# فحص صلاحيات الملفات
sudo chmod -R 755 /var/www/progres-webtu
sudo chown -R www-data:www-data /var/www/progres-webtu
```

##### مشكلة في الاتصال بقاعدة البيانات
```bash
# اختبار الاتصال
mysql -u progres_user -p -h localhost progres_webtu

# فحص إعدادات MySQL
sudo systemctl status mysql
```

##### مشكلة CORS في الإنتاج
```apache
# في .htaccess
Header always set Access-Control-Allow-Origin "https://your-domain.com"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
```

### 10. قائمة مراجعة النشر

#### قبل النشر
- [ ] اختبار جميع الوظائف محلياً
- [ ] تحديث كلمات المرور الافتراضية
- [ ] تحديث إعدادات قاعدة البيانات
- [ ] تحديث مسارات API
- [ ] إعداد SSL
- [ ] تكوين النسخ الاحتياطية

#### بعد النشر
- [ ] اختبار تسجيل الدخول لجميع أنواع المستخدمين
- [ ] اختبار جميع الوظائف الأساسية
- [ ] فحص logs للأخطاء
- [ ] اختبار الأداء
- [ ] تفعيل المراقبة
- [ ] توثيق إعدادات النشر

### 11. الدعم والصيانة

#### مراقبة دورية
- فحص logs يومياً
- مراقبة استخدام الموارد
- اختبار النسخ الاحتياطية شهرياً
- تحديث النظام والتطبيقات

#### تحديثات الأمان
- تطبيق تحديثات النظام
- مراجعة صلاحيات المستخدمين
- تحديث كلمات المرور دورياً
- مراقبة محاولات الاختراق

### 12. خطة الطوارئ

#### في حالة تعطل النظام
1. فحص حالة الخدمات
2. مراجعة logs للأخطاء
3. استعادة من النسخة الاحتياطية إذا لزم الأمر
4. إشعار المستخدمين بالمشكلة
5. توثيق المشكلة والحل

#### معلومات الاتصال للطوارئ
- مدير النظام: [البريد الإلكتروني]
- مدير قاعدة البيانات: [البريد الإلكتروني]
- مزود الاستضافة: [معلومات الاتصال]
