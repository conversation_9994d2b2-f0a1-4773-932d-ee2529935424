// Admin Controller
class AdminController {
    constructor() {
        this.currentUser = null;
    }

    // Login functionality
    async login(username, password) {
        try {
            const data = {
                action: 'login',
                username: username,
                password: password
            };

            const result = await makeApiCall(API_CONFIG.endpoints.admin.login, data);
            
            if (result.success) {
                this.currentUser = new Admin(result.user);
                localStorage.setItem('currentUser', JSON.stringify(result.user));
                localStorage.setItem('userType', 'admin');
                return { success: true, user: this.currentUser };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'Login failed' };
        }
    }

    // Manage users (activate, deactivate, delete)
    async manageUsers(operation, userId) {
        try {
            const data = {
                action: 'manageUsers',
                operation: operation,
                user_id: userId
            };

            const result = await makeApiCall(API_CONFIG.endpoints.admin.manageUsers, data);
            return result;
        } catch (error) {
            console.error('Manage users error:', error);
            return { success: false, message: 'Failed to manage users' };
        }
    }

    // Get system statistics
    async getSystemStats() {
        try {
            const data = {
                action: 'getSystemStats'
            };

            const result = await makeApiCall(API_CONFIG.endpoints.admin.getSystemStats, data);
            return result;
        } catch (error) {
            console.error('Get system stats error:', error);
            return { success: false, message: 'Failed to get system statistics' };
        }
    }

    // Handle technical issues
    async handleTechnicalIssues(operation, issueData) {
        try {
            const data = {
                action: 'handleTechnicalIssues',
                operation: operation,
                ...issueData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.admin.handleTechnicalIssues, data);
            return result;
        } catch (error) {
            console.error('Handle technical issues error:', error);
            return { success: false, message: 'Failed to handle technical issues' };
        }
    }

    // Set user permissions
    async setPermissions(userId, permissions) {
        try {
            const data = {
                action: 'setPermissions',
                user_id: userId,
                permissions: permissions
            };

            const result = await makeApiCall(API_CONFIG.endpoints.admin.setPermissions, data);
            return result;
        } catch (error) {
            console.error('Set permissions error:', error);
            return { success: false, message: 'Failed to set permissions' };
        }
    }

    // Get all users
    async getAllUsers() {
        try {
            const data = {
                action: 'getAllUsers'
            };

            const result = await makeApiCall(API_CONFIG.endpoints.admin.getAllUsers, data);
            
            if (result.success) {
                return {
                    success: true,
                    users: result.users.map(user => {
                        switch(user.user_type) {
                            case 'Student':
                                return new Student(user);
                            case 'Teacher':
                                return new Teacher(user);
                            case 'Agent':
                                return new Agent(user);
                            default:
                                return new User(user);
                        }
                    })
                };
            }
            return result;
        } catch (error) {
            console.error('Get all users error:', error);
            return { success: false, message: 'Failed to get users' };
        }
    }

    // Get all problems
    async getAllProblems() {
        try {
            const data = {
                action: 'getAllProblems'
            };

            const result = await makeApiCall(API_CONFIG.endpoints.admin.getAllProblems, data);
            
            if (result.success) {
                return {
                    success: true,
                    problems: result.problems.map(problem => new Problem(problem))
                };
            }
            return result;
        } catch (error) {
            console.error('Get all problems error:', error);
            return { success: false, message: 'Failed to get problems' };
        }
    }

    // Logout
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userType');
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Load user from localStorage
    loadUserFromStorage() {
        const userData = localStorage.getItem('currentUser');
        const userType = localStorage.getItem('userType');
        
        if (userData && userType === 'admin') {
            this.currentUser = new Admin(JSON.parse(userData));
            return true;
        }
        return false;
    }
}

// Create global instance
const adminController = new AdminController();
