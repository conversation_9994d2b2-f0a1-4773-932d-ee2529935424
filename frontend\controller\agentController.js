// Agent Controller
class AgentController {
    constructor() {
        this.currentUser = null;
    }

    // Login functionality
    async login(username, password) {
        try {
            const data = {
                action: 'login',
                username: username,
                password: password
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.login, data);
            
            if (result.success) {
                this.currentUser = new Agent(result.user);
                localStorage.setItem('currentUser', JSON.stringify(result.user));
                localStorage.setItem('userType', 'agent');
                return { success: true, user: this.currentUser };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'Login failed' };
        }
    }

    // Manage students (add, update, delete)
    async manageStudent(operation, studentData) {
        try {
            const data = {
                action: 'manageStudent',
                operation: operation,
                ...studentData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.manageStudent, data);
            return result;
        } catch (error) {
            console.error('Manage student error:', error);
            return { success: false, message: 'Failed to manage student' };
        }
    }

    // Manage groups and sections
    async manageGroups(operation, groupData) {
        try {
            const data = {
                action: 'manageGroups',
                operation: operation,
                ...groupData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.manageGroups, data);
            return result;
        } catch (error) {
            console.error('Manage groups error:', error);
            return { success: false, message: 'Failed to manage groups' };
        }
    }

    // Manage grades
    async manageGrades(gradeData) {
        try {
            const data = {
                action: 'manageGrades',
                ...gradeData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.manageGrades, data);
            return result;
        } catch (error) {
            console.error('Manage grades error:', error);
            return { success: false, message: 'Failed to manage grades' };
        }
    }

    // Issue certificate
    async issueCertificate(certificateType) {
        try {
            const data = {
                action: 'issueCertificate',
                type: certificateType
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.issueCertificate, data);
            return result;
        } catch (error) {
            console.error('Issue certificate error:', error);
            return { success: false, message: 'Failed to issue certificate' };
        }
    }

    // Process student requests
    async processRequests() {
        try {
            const data = {
                action: 'processRequests'
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.processRequests, data);
            return result;
        } catch (error) {
            console.error('Process requests error:', error);
            return { success: false, message: 'Failed to process requests' };
        }
    }

    // Manage absences
    async manageAbsences(absenceData) {
        try {
            const data = {
                action: 'manageAbsences',
                ...absenceData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.manageAbsences, data);
            return result;
        } catch (error) {
            console.error('Manage absences error:', error);
            return { success: false, message: 'Failed to manage absences' };
        }
    }

    // Get all students
    async getAllStudents() {
        try {
            const data = {
                action: 'getAllStudents'
            };

            const result = await makeApiCall(API_CONFIG.endpoints.agent.getAllStudents, data);
            
            if (result.success) {
                return {
                    success: true,
                    students: result.students.map(student => new Student(student))
                };
            }
            return result;
        } catch (error) {
            console.error('Get all students error:', error);
            return { success: false, message: 'Failed to get students' };
        }
    }

    // Logout
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userType');
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Load user from localStorage
    loadUserFromStorage() {
        const userData = localStorage.getItem('currentUser');
        const userType = localStorage.getItem('userType');
        
        if (userData && userType === 'agent') {
            this.currentUser = new Agent(JSON.parse(userData));
            return true;
        }
        return false;
    }
}

// Create global instance
const agentController = new AgentController();
