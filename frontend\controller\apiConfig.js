// API Configuration
const API_CONFIG = {
    baseUrl: 'http://localhost/progres-webtu/backend/server',
    endpoints: {
        student: {
            login: '/studentApi.php',
            register: '/studentApi.php',
            getSchedule: '/studentApi.php',
            getGrades: '/studentApi.php',
            getGroups: '/studentApi.php',
            requestCertificate: '/studentApi.php',
            requestAcademicLeave: '/studentApi.php',
            getDebts: '/studentApi.php',
            getStudentCard: '/studentApi.php'
        },
        teacher: {
            login: '/teacherApi.php',
            getSchedule: '/teacherApi.php',
            uploadGrades: '/teacherApi.php',
            getModules: '/teacherApi.php',
            getNotifications: '/teacherApi.php',
            sendMessage: '/teacherApi.php'
        },
        agent: {
            login: '/agentApi.php',
            manageStudent: '/agentApi.php',
            manageGroups: '/agentApi.php',
            manageGrades: '/agentApi.php',
            issueCertificate: '/agentApi.php',
            processRequests: '/agentApi.php',
            manageAbsences: '/agentApi.php',
            getAllStudents: '/agentApi.php'
        },
        admin: {
            login: '/adminApi.php',
            manageUsers: '/adminApi.php',
            getSystemStats: '/adminApi.php',
            handleTechnicalIssues: '/adminApi.php',
            setPermissions: '/adminApi.php',
            getAllUsers: '/adminApi.php',
            getAllProblems: '/adminApi.php'
        }
    }
};

// Utility function to make API calls
async function makeApiCall(endpoint, data) {
    try {
        const response = await fetch(API_CONFIG.baseUrl + endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('API call failed:', error);
        return { success: false, message: 'Network error occurred' };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { API_CONFIG, makeApiCall };
}
