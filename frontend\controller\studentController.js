// Student Controller
class StudentController {
    constructor() {
        this.currentUser = null;
    }

    // Login functionality
    async login(username, password) {
        try {
            const data = {
                action: 'login',
                username: username,
                password: password
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.login, data);
            
            if (result.success) {
                this.currentUser = new Student(result.user);
                localStorage.setItem('currentUser', JSON.stringify(result.user));
                localStorage.setItem('userType', 'student');
                return { success: true, user: this.currentUser };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'Login failed' };
        }
    }

    // Registration functionality
    async register(userData) {
        try {
            const data = {
                action: 'register',
                ...userData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.register, data);
            return result;
        } catch (error) {
            console.error('Registration error:', error);
            return { success: false, message: 'Registration failed' };
        }
    }

    // Get class schedule
    async getSchedule(type = 'semestre') {
        try {
            const data = {
                action: 'getSchedule',
                type: type
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.getSchedule, data);
            
            if (result.success) {
                return {
                    success: true,
                    schedules: result.schedules.map(schedule => new Schedule(schedule))
                };
            }
            return result;
        } catch (error) {
            console.error('Get schedule error:', error);
            return { success: false, message: 'Failed to get schedule' };
        }
    }

    // Get grades
    async getGrades() {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'getGrades',
                user_id: this.currentUser.id
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.getGrades, data);
            
            if (result.success) {
                return {
                    success: true,
                    grades: result.grades.map(grade => new Grade(grade))
                };
            }
            return result;
        } catch (error) {
            console.error('Get grades error:', error);
            return { success: false, message: 'Failed to get grades' };
        }
    }

    // Get groups and sections
    async getGroups() {
        try {
            const data = {
                action: 'getGroups'
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.getGroups, data);
            
            if (result.success) {
                return {
                    success: true,
                    groups: result.groups.map(group => new Group(group))
                };
            }
            return result;
        } catch (error) {
            console.error('Get groups error:', error);
            return { success: false, message: 'Failed to get groups' };
        }
    }

    // Request certificate
    async requestCertificate(type) {
        try {
            const data = {
                action: 'requestCertificate',
                type: type
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.requestCertificate, data);
            return result;
        } catch (error) {
            console.error('Request certificate error:', error);
            return { success: false, message: 'Failed to request certificate' };
        }
    }

    // Request academic leave
    async requestAcademicLeave() {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'requestAcademicLeave',
                user_id: this.currentUser.id
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.requestAcademicLeave, data);
            return result;
        } catch (error) {
            console.error('Request academic leave error:', error);
            return { success: false, message: 'Failed to request academic leave' };
        }
    }

    // Get debts
    async getDebts() {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'getDebts',
                user_id: this.currentUser.id
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.getDebts, data);
            return result;
        } catch (error) {
            console.error('Get debts error:', error);
            return { success: false, message: 'Failed to get debts' };
        }
    }

    // Get student card
    async getStudentCard() {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'getStudentCard',
                user_id: this.currentUser.id
            };

            const result = await makeApiCall(API_CONFIG.endpoints.student.getStudentCard, data);
            return result;
        } catch (error) {
            console.error('Get student card error:', error);
            return { success: false, message: 'Failed to get student card' };
        }
    }

    // Logout
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userType');
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Load user from localStorage
    loadUserFromStorage() {
        const userData = localStorage.getItem('currentUser');
        const userType = localStorage.getItem('userType');
        
        if (userData && userType === 'student') {
            this.currentUser = new Student(JSON.parse(userData));
            return true;
        }
        return false;
    }
}

// Create global instance
const studentController = new StudentController();
