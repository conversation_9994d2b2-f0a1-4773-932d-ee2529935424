// Teacher Controller
class TeacherController {
    constructor() {
        this.currentUser = null;
    }

    // Login functionality
    async login(username, password) {
        try {
            const data = {
                action: 'login',
                username: username,
                password: password
            };

            const result = await makeApiCall(API_CONFIG.endpoints.teacher.login, data);
            
            if (result.success) {
                this.currentUser = new Teacher(result.user);
                localStorage.setItem('currentUser', JSON.stringify(result.user));
                localStorage.setItem('userType', 'teacher');
                return { success: true, user: this.currentUser };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'Login failed' };
        }
    }

    // Get teacher schedule
    async getSchedule() {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'getSchedule',
                user_id: this.currentUser.id
            };

            const result = await makeApiCall(API_CONFIG.endpoints.teacher.getSchedule, data);
            
            if (result.success) {
                return {
                    success: true,
                    schedule: new Schedule(result.schedule)
                };
            }
            return result;
        } catch (error) {
            console.error('Get schedule error:', error);
            return { success: false, message: 'Failed to get schedule' };
        }
    }

    // Upload grades
    async uploadGrades(gradeData) {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'uploadGrades',
                user_id: this.currentUser.id,
                ...gradeData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.teacher.uploadGrades, data);
            return result;
        } catch (error) {
            console.error('Upload grades error:', error);
            return { success: false, message: 'Failed to upload grades' };
        }
    }

    // Get teacher modules
    async getModules() {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'getModules',
                user_id: this.currentUser.id
            };

            const result = await makeApiCall(API_CONFIG.endpoints.teacher.getModules, data);
            
            if (result.success) {
                return {
                    success: true,
                    modules: result.modules.map(module => new Module(module))
                };
            }
            return result;
        } catch (error) {
            console.error('Get modules error:', error);
            return { success: false, message: 'Failed to get modules' };
        }
    }

    // Get notifications
    async getNotifications() {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'getNotifications',
                user_id: this.currentUser.id
            };

            const result = await makeApiCall(API_CONFIG.endpoints.teacher.getNotifications, data);
            return result;
        } catch (error) {
            console.error('Get notifications error:', error);
            return { success: false, message: 'Failed to get notifications' };
        }
    }

    // Send message to students
    async sendMessage(messageData) {
        try {
            if (!this.currentUser) {
                return { success: false, message: 'User not logged in' };
            }

            const data = {
                action: 'sendMessage',
                user_id: this.currentUser.id,
                ...messageData
            };

            const result = await makeApiCall(API_CONFIG.endpoints.teacher.sendMessage, data);
            return result;
        } catch (error) {
            console.error('Send message error:', error);
            return { success: false, message: 'Failed to send message' };
        }
    }

    // Logout
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userType');
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Load user from localStorage
    loadUserFromStorage() {
        const userData = localStorage.getItem('currentUser');
        const userType = localStorage.getItem('userType');
        
        if (userData && userType === 'teacher') {
            this.currentUser = new Teacher(JSON.parse(userData));
            return true;
        }
        return false;
    }
}

// Create global instance
const teacherController = new TeacherController();
