// Schedule Model
class Schedule {
    constructor(data = {}) {
        this.id = data.ID_emploi || null;
        this.semestre = data.semestre || '';
        this.type = data.type || '';
        this.matieres = data.les_matieres || '';
        this.heures = data.les_heures || '';
        this.jours = data.les_jours || '';
        this.locaux = data.les_locaux || '';
    }

    getScheduleInfo() {
        return {
            semestre: this.semestre,
            type: this.type,
            matieres: this.matieres,
            heures: this.heures,
            jours: this.jours,
            locaux: this.locaux
        };
    }
}

// Grade Model
class Grade {
    constructor(data = {}) {
        this.id = data.ID_note || null;
        this.type = data.type || '';
        this.matieres = data.les_matieres || '';
        this.coefficients = data.les_coefficient || '';
        this.dette = data.dette || 0;
    }

    getGradeInfo() {
        return {
            type: this.type,
            matieres: this.matieres,
            coefficients: this.coefficients,
            dette: this.dette
        };
    }
}

// Grade Report Model
class GradeReport {
    constructor(data = {}) {
        this.id = data.ID_RN || null;
        this.notes = data.les_notes || '';
        this.modules = data.les_modules || '';
        this.credit = data.credit || '';
        this.session = data.session || '';
    }

    getReportInfo() {
        return {
            notes: this.notes,
            modules: this.modules,
            credit: this.credit,
            session: this.session
        };
    }
}

// Group Model
class Group {
    constructor(data = {}) {
        this.id = data.ID_section_group || null;
        this.semestre = data.semestre || '';
        this.nomSection = data.nom_section || '';
        this.numGroup = data.num_group || 0;
        this.userId = data.ID_utilisateur || null;
    }

    getGroupInfo() {
        return {
            semestre: this.semestre,
            nomSection: this.nomSection,
            numGroup: this.numGroup
        };
    }
}

// Certificate Model
class Certificate {
    constructor(data = {}) {
        this.id = data.ID_attestation || null;
        this.type = data.type || '';
        this.dateIssued = data.date_issued || new Date().toISOString().split('T')[0];
    }

    getCertificateInfo() {
        return {
            type: this.type,
            dateIssued: this.dateIssued
        };
    }
}

// Module Model
class Module {
    constructor(data = {}) {
        this.id = data.ID_module || null;
        this.nomModule = data.nom_module || '';
        this.niveau = data.niveau || '';
        this.semestre = data.semestre || '';
        this.type = data.type || '';
    }

    getModuleInfo() {
        return {
            nomModule: this.nomModule,
            niveau: this.niveau,
            semestre: this.semestre,
            type: this.type
        };
    }
}

// Problem Model
class Problem {
    constructor(data = {}) {
        this.id = data.ID_prblm || null;
        this.nom = data.nom || '';
        this.contenu = data.contenu || '';
        this.dateCreated = data.date_created || new Date().toISOString().split('T')[0];
    }

    getProblemInfo() {
        return {
            nom: this.nom,
            contenu: this.contenu,
            dateCreated: this.dateCreated
        };
    }
}
