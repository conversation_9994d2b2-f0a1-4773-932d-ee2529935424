// User Model
class User {
    constructor(data = {}) {
        this.id = data.id || null;
        this.nom = data.nom || '';
        this.prenom = data.prenom || '';
        this.dateNaissance = data.date_de_naissance || '';
        this.etablissement = data.etablissement || '';
        this.username = data.nom_utilisateur || '';
        this.userType = data.user_type || '';
    }

    getFullName() {
        return `${this.prenom} ${this.nom}`;
    }

    isValid() {
        return this.nom && this.prenom && this.dateNaissance && this.etablissement;
    }
}

// Student Model
class Student extends User {
    constructor(data = {}) {
        super(data);
        this.anneeBac = data.Annee_bac || '';
        this.cycle = data.cycle || '';
        this.annee = data.annee || '';
        this.diplom = data.diplom || '';
        this.niveau = data.niveau || '';
        this.institu = data.institu || '';
        this.departement = data.departement || '';
        this.specialite = data.specialite || '';
        this.carte = data.carte || '';
    }

    getAcademicInfo() {
        return {
            niveau: this.niveau,
            departement: this.departement,
            specialite: this.specialite,
            cycle: this.cycle,
            annee: this.annee
        };
    }
}

// Teacher Model
class Teacher extends User {
    constructor(data = {}) {
        super(data);
        this.dateRecrutement = data.date_de_recrutement || '';
        this.niveau = data.niveau || '';
        this.emploiTempsId = data.ID_emploi_de_temps || null;
    }

    getTeachingInfo() {
        return {
            niveau: this.niveau,
            dateRecrutement: this.dateRecrutement
        };
    }
}

// Agent Model
class Agent extends User {
    constructor(data = {}) {
        super(data);
        this.dateRecrutement = data.date_recrutement || '';
        this.niveau = data.niveau || '';
    }

    getAgentInfo() {
        return {
            niveau: this.niveau,
            dateRecrutement: this.dateRecrutement
        };
    }
}

// Admin Model
class Admin extends User {
    constructor(data = {}) {
        super(data);
        this.permissions = data.permissions || [];
    }

    hasPermission(permission) {
        return this.permissions.includes(permission);
    }
}
