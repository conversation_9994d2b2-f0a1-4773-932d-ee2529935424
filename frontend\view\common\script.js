// Main script for login functionality
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    
    // Check if user is already logged in
    checkExistingLogin();
    
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const userType = document.getElementById('userType').value;
        
        if (!username || !password || !userType) {
            showAlert('يرجى ملء جميع الحقول', 'error');
            return;
        }
        
        // Show loading state
        const submitBtn = loginForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'جاري تسجيل الدخول...';
        submitBtn.disabled = true;
        
        try {
            let result;
            
            switch(userType) {
                case 'student':
                    result = await studentController.login(username, password);
                    break;
                case 'teacher':
                    result = await teacherController.login(username, password);
                    break;
                case 'agent':
                    result = await agentController.login(username, password);
                    break;
                case 'admin':
                    result = await adminController.login(username, password);
                    break;
                default:
                    showAlert('نوع المستخدم غير صحيح', 'error');
                    return;
            }
            
            if (result.success) {
                showAlert('تم تسجيل الدخول بنجاح', 'success');
                
                // Redirect based on user type
                setTimeout(() => {
                    switch(userType) {
                        case 'student':
                            window.location.href = '../student/dashboard.html';
                            break;
                        case 'teacher':
                            window.location.href = '../teacher/dashboard.html';
                            break;
                        case 'agent':
                            window.location.href = '../agent/dashboard.html';
                            break;
                        case 'admin':
                            window.location.href = '../admin/dashboard.html';
                            break;
                    }
                }, 1500);
            } else {
                showAlert(result.message || 'فشل في تسجيل الدخول', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            showAlert('حدث خطأ في الاتصال بالخادم', 'error');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });
});

function checkExistingLogin() {
    const userType = localStorage.getItem('userType');
    const currentUser = localStorage.getItem('currentUser');
    
    if (userType && currentUser) {
        // User is already logged in, redirect to appropriate dashboard
        switch(userType) {
            case 'student':
                if (studentController.loadUserFromStorage()) {
                    window.location.href = '../student/dashboard.html';
                }
                break;
            case 'teacher':
                if (teacherController.loadUserFromStorage()) {
                    window.location.href = '../teacher/dashboard.html';
                }
                break;
            case 'agent':
                if (agentController.loadUserFromStorage()) {
                    window.location.href = '../agent/dashboard.html';
                }
                break;
            case 'admin':
                if (adminController.loadUserFromStorage()) {
                    window.location.href = '../admin/dashboard.html';
                }
                break;
        }
    }
}

function showAlert(message, type) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    // Insert alert after the login form
    const loginContainer = document.querySelector('.login-container');
    loginContainer.appendChild(alert);
    
    // Auto remove alert after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// Utility function to handle navigation
function navigateTo(url) {
    window.location.href = url;
}

// Handle browser back button
window.addEventListener('popstate', function(event) {
    // Clear any stored user data if navigating back to login
    if (window.location.pathname.includes('index.html')) {
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userType');
    }
});
