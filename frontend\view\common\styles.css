/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo h1 {
    color: white;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.logo p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    font-weight: 400;
}

/* Main Content */
.main-content {
    display: grid;
    gap: 30px;
}

.welcome-section {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.welcome-section h2 {
    color: #4a5568;
    font-size: 2.5rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.welcome-section p {
    color: #718096;
    font-size: 1.2rem;
    line-height: 1.6;
}

/* Login Section */
.login-section {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-container h3 {
    text-align: center;
    color: #4a5568;
    font-size: 1.8rem;
    margin-bottom: 30px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #4a5568;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.register-link {
    text-align: center;
    margin-top: 20px;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.register-link a:hover {
    text-decoration: underline;
}

/* Features Section */
.features-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.features-section h3 {
    text-align: center;
    color: #4a5568;
    font-size: 2rem;
    margin-bottom: 30px;
    font-weight: 600;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.feature-card {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 25px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
}

.feature-card h4 {
    color: #2d3748;
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
    text-align: center;
}

.feature-card ul {
    list-style: none;
}

.feature-card li {
    color: #4a5568;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
    position: relative;
    padding-right: 20px;
}

.feature-card li:last-child {
    border-bottom: none;
}

.feature-card li:before {
    content: "✓";
    position: absolute;
    right: 0;
    color: #48bb78;
    font-weight: bold;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.footer p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

/* Alert Styles */
.alert {
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background-color: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.alert-error {
    background-color: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .welcome-section h2 {
        font-size: 1.8rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .login-container {
        margin: 0 10px;
    }
}
