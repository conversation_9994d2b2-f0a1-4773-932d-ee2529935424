// Student Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    if (!studentController.loadUserFromStorage()) {
        window.location.href = '../common/index.html';
        return;
    }
    
    // Initialize dashboard
    initializeDashboard();
    
    // Setup certificate form
    setupCertificateForm();
});

function initializeDashboard() {
    const user = studentController.currentUser;
    if (user) {
        document.getElementById('welcomeMessage').textContent = `مرحباً ${user.getFullName()}`;
        const academicInfo = user.getAcademicInfo();
        document.getElementById('userDetails').textContent = 
            `${academicInfo.niveau} - ${academicInfo.departement} - ${academicInfo.specialite}`;
    }
}

function logout() {
    studentController.logout();
    window.location.href = '../common/index.html';
}

// Schedule functions
function showSchedule() {
    document.getElementById('scheduleModal').style.display = 'block';
}

async function loadSchedule(type) {
    const content = document.getElementById('scheduleContent');
    content.innerHTML = '<div class="loading"></div>';
    
    try {
        const result = await studentController.getSchedule(type);
        
        if (result.success && result.schedules.length > 0) {
            let html = `<h4>جدول ${type === 'semestre' ? 'الفصل' : 'الامتحانات'}</h4>`;
            html += '<table class="data-table">';
            html += '<thead><tr><th>الفصل</th><th>المواد</th><th>الساعات</th><th>الأيام</th><th>القاعات</th></tr></thead>';
            html += '<tbody>';
            
            result.schedules.forEach(schedule => {
                html += `<tr>
                    <td>${schedule.semestre}</td>
                    <td>${schedule.matieres || 'غير محدد'}</td>
                    <td>${schedule.heures || 'غير محدد'}</td>
                    <td>${schedule.jours || 'غير محدد'}</td>
                    <td>${schedule.locaux || 'غير محدد'}</td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            content.innerHTML = html;
        } else {
            content.innerHTML = '<p>لا توجد جداول متاحة حالياً</p>';
        }
    } catch (error) {
        console.error('Error loading schedule:', error);
        content.innerHTML = '<p>حدث خطأ في تحميل الجدول</p>';
    }
}

// Grades functions
async function showGrades() {
    document.getElementById('gradesModal').style.display = 'block';
    const content = document.getElementById('gradesContent');
    content.innerHTML = '<div class="loading"></div>';
    
    try {
        const result = await studentController.getGrades();
        
        if (result.success && result.grades.length > 0) {
            let html = '<h4>النتائج والدرجات</h4>';
            html += '<table class="data-table">';
            html += '<thead><tr><th>النوع</th><th>المواد</th><th>المعاملات</th><th>الديون</th></tr></thead>';
            html += '<tbody>';
            
            result.grades.forEach(grade => {
                html += `<tr>
                    <td>${grade.type}</td>
                    <td>${grade.matieres || 'غير محدد'}</td>
                    <td>${grade.coefficients || 'غير محدد'}</td>
                    <td>${grade.dette || 0} دج</td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            content.innerHTML = html;
        } else {
            content.innerHTML = '<p>لا توجد نتائج متاحة حالياً</p>';
        }
    } catch (error) {
        console.error('Error loading grades:', error);
        content.innerHTML = '<p>حدث خطأ في تحميل النتائج</p>';
    }
}

// Groups functions
async function showGroups() {
    document.getElementById('groupsModal').style.display = 'block';
    const content = document.getElementById('groupsContent');
    content.innerHTML = '<div class="loading"></div>';
    
    try {
        const result = await studentController.getGroups();
        
        if (result.success && result.groups.length > 0) {
            let html = '<h4>المجموعات والأقسام</h4>';
            html += '<table class="data-table">';
            html += '<thead><tr><th>الفصل</th><th>اسم القسم</th><th>رقم المجموعة</th></tr></thead>';
            html += '<tbody>';
            
            result.groups.forEach(group => {
                html += `<tr>
                    <td>${group.semestre}</td>
                    <td>${group.nomSection}</td>
                    <td>${group.numGroup}</td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            content.innerHTML = html;
        } else {
            content.innerHTML = '<p>لا توجد مجموعات متاحة حالياً</p>';
        }
    } catch (error) {
        console.error('Error loading groups:', error);
        content.innerHTML = '<p>حدث خطأ في تحميل المجموعات</p>';
    }
}

// Certificate functions
function requestCertificate() {
    document.getElementById('certificateModal').style.display = 'block';
}

function setupCertificateForm() {
    const form = document.getElementById('certificateForm');
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const certificateType = document.getElementById('certificateType').value;
        
        if (!certificateType) {
            alert('يرجى اختيار نوع الشهادة');
            return;
        }
        
        try {
            const result = await studentController.requestCertificate(certificateType);
            
            if (result.success) {
                alert('تم تقديم طلب الشهادة بنجاح');
                closeModal('certificateModal');
                form.reset();
            } else {
                alert(result.message || 'فشل في تقديم طلب الشهادة');
            }
        } catch (error) {
            console.error('Error requesting certificate:', error);
            alert('حدث خطأ في تقديم الطلب');
        }
    });
}

// Academic leave function
async function requestAcademicLeave() {
    try {
        const result = await studentController.requestAcademicLeave();
        
        if (result.success) {
            alert('تم تقديم طلب الإجازة الأكاديمية بنجاح');
        } else {
            alert(result.message || 'فشل في تقديم طلب الإجازة');
        }
    } catch (error) {
        console.error('Error requesting academic leave:', error);
        alert('حدث خطأ في تقديم الطلب');
    }
}

// Debts functions
async function showDebts() {
    document.getElementById('debtsModal').style.display = 'block';
    const content = document.getElementById('debtsContent');
    content.innerHTML = '<div class="loading"></div>';
    
    try {
        const result = await studentController.getDebts();
        
        if (result.success) {
            const debt = result.debt || 0;
            let html = '<h4>حالة الديون والرسوم</h4>';
            html += '<div class="debt-info">';
            html += `<p><strong>إجمالي الديون:</strong> ${debt} دج</p>`;
            
            if (debt > 0) {
                html += '<p style="color: #e53e3e;">يوجد ديون مستحقة</p>';
                html += '<button class="card-btn" onclick="downloadReceipt()">تحميل الإيصال</button>';
            } else {
                html += '<p style="color: #48bb78;">لا توجد ديون مستحقة</p>';
            }
            
            html += '</div>';
            content.innerHTML = html;
        } else {
            content.innerHTML = '<p>حدث خطأ في تحميل بيانات الديون</p>';
        }
    } catch (error) {
        console.error('Error loading debts:', error);
        content.innerHTML = '<p>حدث خطأ في تحميل بيانات الديون</p>';
    }
}

function downloadReceipt() {
    alert('سيتم تحميل الإيصال الإلكتروني قريباً');
}

// Student card functions
async function showStudentCard() {
    document.getElementById('cardModal').style.display = 'block';
    const content = document.getElementById('cardContent');
    content.innerHTML = '<div class="loading"></div>';
    
    try {
        const result = await studentController.getStudentCard();
        
        if (result.success) {
            let html = '<h4>بطاقة الطالب</h4>';
            html += '<div class="card-info">';
            
            if (result.card) {
                html += `<p><strong>رقم البطاقة:</strong> ${result.card}</p>`;
                html += '<p style="color: #48bb78;">البطاقة صالحة</p>';
            } else {
                html += '<p>لا توجد بطاقة مسجلة</p>';
                html += '<button class="card-btn" onclick="requestNewCard()">طلب بطاقة جديدة</button>';
            }
            
            html += '</div>';
            content.innerHTML = html;
        } else {
            content.innerHTML = '<p>حدث خطأ في تحميل بيانات البطاقة</p>';
        }
    } catch (error) {
        console.error('Error loading student card:', error);
        content.innerHTML = '<p>حدث خطأ في تحميل بيانات البطاقة</p>';
    }
}

function requestNewCard() {
    alert('سيتم معالجة طلب البطاقة الجديدة');
}

// Additional services function
function showAdditionalServices() {
    alert('خدمات إضافية: التواصل مع الإدارة، تقديم الشكاوى، الدعم التقني');
}

// Modal functions
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}
