// Student registration functionality
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    
    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(registerForm);
        const userData = {
            nom: formData.get('nom'),
            prenom: formData.get('prenom'),
            date_naissance: formData.get('date_naissance'),
            etablissement: formData.get('etablissement'),
            username: formData.get('username'),
            password: formData.get('password'),
            annee_bac: formData.get('annee_bac'),
            cycle: formData.get('cycle'),
            annee: formData.get('annee'),
            niveau: formData.get('niveau'),
            departement: formData.get('departement'),
            specialite: formData.get('specialite')
        };
        
        // Validate form data
        if (!validateFormData(userData)) {
            return;
        }
        
        // Show loading state
        const submitBtn = registerForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'جاري التسجيل...';
        submitBtn.disabled = true;
        
        try {
            const result = await studentController.register(userData);
            
            if (result.success) {
                showAlert('تم التسجيل بنجاح! يمكنك الآن تسجيل الدخول', 'success');
                
                // Clear form
                registerForm.reset();
                
                // Redirect to login page after 3 seconds
                setTimeout(() => {
                    window.location.href = '../common/index.html';
                }, 3000);
            } else {
                showAlert(result.message || 'فشل في التسجيل', 'error');
            }
        } catch (error) {
            console.error('Registration error:', error);
            showAlert('حدث خطأ في الاتصال بالخادم', 'error');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });
    
    // Add real-time validation
    addFormValidation();
});

function validateFormData(userData) {
    // Check required fields
    const requiredFields = [
        'nom', 'prenom', 'date_naissance', 'etablissement', 
        'username', 'password', 'annee_bac', 'cycle', 
        'annee', 'niveau', 'departement', 'specialite'
    ];
    
    for (let field of requiredFields) {
        if (!userData[field] || userData[field].trim() === '') {
            showAlert(`يرجى ملء حقل ${getFieldLabel(field)}`, 'error');
            return false;
        }
    }
    
    // Validate username (should be unique and valid format)
    if (userData.username.length < 3) {
        showAlert('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
        return false;
    }
    
    // Validate password
    if (userData.password.length < 6) {
        showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return false;
    }
    
    // Validate year fields
    const currentYear = new Date().getFullYear();
    if (userData.annee_bac < 2000 || userData.annee_bac > currentYear) {
        showAlert('سنة البكالوريا غير صحيحة', 'error');
        return false;
    }
    
    if (userData.annee < 2020 || userData.annee > currentYear + 1) {
        showAlert('السنة الحالية غير صحيحة', 'error');
        return false;
    }
    
    // Validate date of birth
    const birthDate = new Date(userData.date_naissance);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    if (age < 16 || age > 80) {
        showAlert('تاريخ الميلاد غير صحيح', 'error');
        return false;
    }
    
    return true;
}

function getFieldLabel(field) {
    const labels = {
        'nom': 'الاسم العائلي',
        'prenom': 'الاسم الشخصي',
        'date_naissance': 'تاريخ الميلاد',
        'etablissement': 'المؤسسة',
        'username': 'اسم المستخدم',
        'password': 'كلمة المرور',
        'annee_bac': 'سنة البكالوريا',
        'cycle': 'الطور',
        'annee': 'السنة الحالية',
        'niveau': 'المستوى',
        'departement': 'القسم',
        'specialite': 'التخصص'
    };
    
    return labels[field] || field;
}

function addFormValidation() {
    // Add real-time validation for username
    const usernameField = document.getElementById('username');
    usernameField.addEventListener('blur', function() {
        const username = this.value.trim();
        if (username && username.length < 3) {
            this.style.borderColor = '#e53e3e';
            showFieldError(this, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
        } else {
            this.style.borderColor = '#e2e8f0';
            clearFieldError(this);
        }
    });
    
    // Add real-time validation for password
    const passwordField = document.getElementById('password');
    passwordField.addEventListener('blur', function() {
        const password = this.value.trim();
        if (password && password.length < 6) {
            this.style.borderColor = '#e53e3e';
            showFieldError(this, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        } else {
            this.style.borderColor = '#e2e8f0';
            clearFieldError(this);
        }
    });
    
    // Add validation for year fields
    const yearFields = ['annee_bac', 'annee'];
    yearFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        field.addEventListener('blur', function() {
            const year = parseInt(this.value);
            const currentYear = new Date().getFullYear();
            
            if (fieldId === 'annee_bac' && (year < 2000 || year > currentYear)) {
                this.style.borderColor = '#e53e3e';
                showFieldError(this, 'سنة البكالوريا غير صحيحة');
            } else if (fieldId === 'annee' && (year < 2020 || year > currentYear + 1)) {
                this.style.borderColor = '#e53e3e';
                showFieldError(this, 'السنة الحالية غير صحيحة');
            } else {
                this.style.borderColor = '#e2e8f0';
                clearFieldError(this);
            }
        });
    });
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#e53e3e';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '5px';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function showAlert(message, type) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    // Insert alert at the top of the register container
    const registerContainer = document.querySelector('.register-container');
    registerContainer.insertBefore(alert, registerContainer.firstChild);
    
    // Auto remove alert after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
