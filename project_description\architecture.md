# Project Architecture

The project is divided into two main directories:

## 1. Frontend
Consists of three main subdirectories:
- **Model**  
  Contains classes that represent the core entities in the frontend. These classes hold data only and do not include methods.

- **Controller**  
  Contains JavaScript files, where each file corresponds to a set of use cases for a specific type of user (e.g., studentController.js, teacherController.js).  
  - Each file contains functions that implement the use cases for that user.
  - All API endpoint configurations are stored in a single file (apiConfig.js) to allow easy modification of the `baseUrl` in one place.

- **View**  
  Contains directories representing different user interface workflows.  
  - Inside each directory, there are HTML, CSS, and sometimes a JS file for view-specific interactions.

## 2. Backend
Consists of:
- **SQL Database**  
  A single file (database.sql) that contains all the relational model tables and their relationships.

- **server Directory**  
  Contains PHP files responsible for handling requests from the frontend.  
  - All PHP files in this directory should include the following headers to enable CORS and return JSON:
    ```php
    header("Access-Control-Allow-Origin: *");
    header("Content-Type: application/json; charset=UTF-8");
    header("Access-Control-Allow-Methods: POST");
    header("Access-Control-Max-Age: 3600");
    header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
    ```
  - don't use password hashing in storing passwords in the database
  - Each PHP file handles a specific set of use cases (e.g., studentApi.php, teacherApi.php), following the same use case separation principle as in the frontend.
