# Relational Model

Below is a comprehensive description of the relational model used in the database:

1. **Utilisateur**  
   - ID_utilisateur (PRIMARY KEY)  
   - nom (VARCHAR)  
   - prenom (VARCHAR)  
   - date_de_naissance (DATE)  
   - etablissement (VARCHAR)

2. **Compte**  
   - ID_compte (PRIMARY KEY)  
   - nom_utilisateur (VARCHAR)  
   - mot_de_passe (VARCHAR)  
   - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)

3. **Enseignant**  
   - ID_utilisateur (PRIMARY KEY, FOREIGN KEY → Utilisateur.ID_utilisateur)  
   - date_de_recrutement (DATE)  
   - niveau (VARCHAR)  
   - ID_emploi_de_temps (FOREIGN KEY → Emploi_du_temps.ID_emploi)

4. **Étudiant**  
   - ID_utilisateur (PRIMARY KEY, FOREIGN KEY → Utilisateur.ID_utilisateur)  
   - Anne<PERSON>_bac (YEAR)  
   - cycle (VARCHAR)  
   - annee (YEAR)  
   - diplom (VARCHAR)  
   - niveau (VARCHAR)  
   - institu (VARCHAR)  
   - departement (VARCHAR)  
   - date (DATE)  
   - specialite (VARCHAR)  
   - carte (VARCHAR)  
   - ID_note (FOREIGN KEY → Note.ID_note)  
   - ID_RN (FOREIGN KEY → Releve_de_note.ID_RN)  
   - ID_attestation (FOREIGN KEY → Attestation.ID_attestation)

5. **Agent**  
   - ID_utilisateur (PRIMARY KEY, FOREIGN KEY → Utilisateur.ID_utilisateur)  
   - date_recrutement (DATE)  
   - niveau (VARCHAR)  
   - ID_emploi (FOREIGN KEY → Emploi_du_temps.ID_emploi)  
   - ID_absence (FOREIGN KEY → Absence_et_exclu.ID_absance)  
   - ID_CA (FOREIGN KEY → Conge_Academique.ID_CA)  
   - ID_note (FOREIGN KEY → Note.ID_note)  
   - ID_RN (FOREIGN KEY → Releve_de_note.ID_RN)  
   - ID_attestation (FOREIGN KEY → Attestation.ID_attestation)  
   - ID_section_group (FOREIGN KEY → Section_et_group.ID_section_group)

6. **Admin**  
   - ID_compte (FOREIGN KEY → Compte.ID_compte)  
   - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)  
   - ID_prblm (FOREIGN KEY → Probleme.ID_prblm)

7. **Emploi_du_temps**  
   - ID_emploi (PRIMARY KEY)  
   - semestre (VARCHAR)  
   - type (ENUM: 'examen', 'semestre', etc.)  
   - les_matieres (TEXT)  
   - les_heures (VARCHAR)  
   - les_jours (VARCHAR)  
   - les_locaux (VARCHAR)

8. **Section_et_group**  
   - ID_section_group (PRIMARY KEY)  
   - semestre (VARCHAR)  
   - nom_section (VARCHAR)  
   - num_group (INT)  
   - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)

9. **Attestation**  
   - ID_attestation (PRIMARY KEY)  
   - type (ENUM: 'scolarite', 'inscription')

10. **Releve_de_note**  
    - ID_RN (PRIMARY KEY)  
    - les_notes (TEXT)  
    - les_modules (TEXT)  
    - credit (VARCHAR)  
    - session (ENUM: 'normale', 'rattrapage')

11. **Absence_et_exclu**  
    - ID_absance (PRIMARY KEY)  
    - num_absance (INT)  
    - conge_academique (VARCHAR)

12. **Note**  
    - ID_note (PRIMARY KEY)  
    - type (ENUM: 'examen', 'controle_continu', 'rattrapage')  
    - les_matieres (TEXT)  
    - les_coefficient (TEXT)  
    - dette (DECIMAL)

13. **Conge_Academique**  
    - ID_CA (PRIMARY KEY)  
    - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)

14. **Probleme**  
    - ID_prblm (PRIMARY KEY)  
    - nom (VARCHAR)  
    - contenu (TEXT)

15. **Signale**  
    - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)  
    - ID_prblm (FOREIGN KEY → Probleme.ID_prblm)

16. **Consulter**  
    - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)  
    - ID_emploi_du_temps (FOREIGN KEY → Emploi_du_temps.ID_emploi)

17. **Importe**  
    - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)  
    - ID_note (FOREIGN KEY → Note.ID_note)

18. **Enseignant_module**  
    - ID_ens_mod (PRIMARY KEY)  
    - ID_utilisateur (FOREIGN KEY → Utilisateur.ID_utilisateur)  
    - ID_module (FOREIGN KEY → Module.ID_module)

19. **Module**  
    - ID_module (PRIMARY KEY)  
    - nom_module (VARCHAR)  
    - niveau (VARCHAR)  
    - semestre (VARCHAR)  
    - type (VARCHAR)
