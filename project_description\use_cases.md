# Use Cases

Below is a concise description of the main use cases for each user category without losing essential details:

## 1. Student
- **Online Registration**  
  - Create a new account on the platform by filling in the required personal information.
- **View Class Schedule**  
  - Browse the lecture and exam timetable and select between the semester or exam schedule.
- **View Groups and Sections**  
  - Browse the groups and sections for the current semester.
- **Request Certificate/Official Document**  
  - Submit a request for an official document (enrollment certificate, grade transcript, etc.) and specify the document type.
- **View Grade Report**  
  - Review grades for all semesters or exam sessions.
- **Request Academic Leave**  
  - Submit an academic leave request with an attached justification (e.g., medical note).
- **Print and Download Electronic Receipt**  
  - View tuition payments and debt status, and download the electronic receipt.
- **Check Debts**  
  - View details of tuition fees and outstanding debts.
- **View Pass Rate per Course**  
  - Display the pass percentage for each course in the semester.
- **View/Request Student ID Card**  
  - Check the validity of the ID card and request issuance or renewal.
- **Access Additional Services**  
  - Communicate with administration via email or chat, and report technical or administrative issues.

## 2. Teacher
- **Login and Review Notifications**  
  - Check new announcements and messages from administration.
- **Import and Upload Grades**  
  - Import a grade file or enter grades manually (exam, continuous assessment, or remedial exams).
- **Handle Remedial Requests**  
  - Review grade appeal requests and make appropriate decisions.
- **View Teacher Schedule**  
  - Browse class and exam schedules and room details.
- **Communicate with Students via Platform**  
  - Send messages or notifications to students regarding grades or academic remarks.

## 3. Administrative Staff
- **Edit Student Records**  
  - Add/update/delete student information and timetables.
- **Manage Groups and Sections**  
  - Create new sections and assign students to appropriate groups.
- **Manage Grade Records**  
  - Upload exam and continuous assessment grades and calculate averages.
- **Prepare Grade Reports and Annual Data**  
  - Generate grade reports for semesters and compile annual academic data.
- **Issue Certificates and Official Documents**  
  - Create required certificates (enrollment, graduation certificate, etc.) and ensure data accuracy.
- **Process Student Requests**  
  - Review academic leave requests, debt inquiries, and exemptions.
- **Manage Absences and Exclusions**  
  - Record absences and process exclusion or academic probation cases.
- **Communicate with Senior Administration**  
  - Escalate technical or administrative issues to supervisors.

## 4. Administrator
- **Manage User Accounts**  
  - Activate/deactivate user accounts (students, teachers, and staff).
- **System Maintenance and Updates**  
  - Perform regular updates and system diagnostics.
- **Handle Technical Issues**  
  - Review and resolve bugs and technical problems.
- **Monitor Platform Performance**  
  - Review usage reports and analyze potential issues.
- **Set User Permissions**  
  - Grant or revoke access rights to specific features on the platform.
