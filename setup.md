# دليل الإعداد السريع - PROGRES WebTU

## الإعداد المحلي (Local Development)

### 1. متطلبات النظام
- XAMPP أو WAMP أو LAMP
- PHP 7.4+
- MySQL 5.7+
- متصفح ويب حديث

### 2. خطوات الإعداد السريع

#### أ. تثبيت XAMPP
1. حمل XAMPP من الموقع الرسمي
2. ثبت XAMPP في المجلد الافتراضي
3. شغل Apache و MySQL من لوحة تحكم XAMPP

#### ب. إعداد قاعدة البيانات
1. افتح phpMyAdmin: `http://localhost/phpmyadmin`
2. أنشئ قاعدة بيانات جديدة باسم `progres_webtu`
3. استورد ملف `backend/database.sql`

#### ج. نسخ ملفات المشروع
1. انسخ مجلد المشروع إلى `C:\xampp\htdocs\progres-webtu`
2. تأكد من أن المسار صحيح

#### د. تعديل إعدادات الاتصال
1. افتح `backend/server/config/database.php`
2. تأكد من الإعدادات التالية:
```php
private $host = "localhost";
private $db_name = "progres_webtu";
private $username = "root";
private $password = ""; // فارغ في XAMPP الافتراضي
```

#### هـ. تعديل إعدادات API
1. افتح `frontend/controller/apiConfig.js`
2. تأكد من المسار الصحيح:
```javascript
baseUrl: 'http://localhost/progres-webtu/backend/server'
```

### 3. تشغيل النظام
1. تأكد من تشغيل Apache و MySQL
2. افتح المتصفح وانتقل إلى:
   `http://localhost/progres-webtu/frontend/view/common/index.html`

### 4. بيانات الدخول الافتراضية

#### مدير النظام
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

#### أستاذ
- اسم المستخدم: `jean.dupont`
- كلمة المرور: `password123`

#### طالب
- اسم المستخدم: `marie.martin`
- كلمة المرور: `password123`

#### موظف إداري
- اسم المستخدم: `ahmed.benali`
- كلمة المرور: `password123`

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
**المشكلة**: "Connection error"
**الحل**:
- تأكد من تشغيل MySQL
- تحقق من بيانات الاتصال في `database.php`
- تأكد من وجود قاعدة البيانات `progres_webtu`

#### 2. خطأ CORS
**المشكلة**: "CORS policy error"
**الحل**:
- تأكد من إضافة CORS headers في ملفات PHP
- استخدم خادم محلي بدلاً من فتح الملفات مباشرة

#### 3. خطأ 404 في API
**المشكلة**: "API endpoint not found"
**الحل**:
- تحقق من مسار `baseUrl` في `apiConfig.js`
- تأكد من وجود ملفات PHP في المجلد الصحيح

#### 4. مشكلة في تسجيل الدخول
**المشكلة**: "Invalid credentials"
**الحل**:
- تأكد من استيراد البيانات الافتراضية من `database.sql`
- تحقق من بيانات الدخول المستخدمة

### 5. اختبار النظام

#### اختبار الطلاب
1. سجل دخول كطالب
2. جرب عرض الجدول الدراسي
3. جرب عرض النتائج
4. جرب طلب شهادة

#### اختبار الأساتذة
1. سجل دخول كأستاذ
2. جرب رفع الدرجات
3. جرب عرض الجدول
4. جرب إرسال رسالة

#### اختبار الموظفين الإداريين
1. سجل دخول كموظف إداري
2. جرب إدارة الطلاب
3. جرب إدارة المجموعات
4. جرب إصدار شهادة

#### اختبار المديرين
1. سجل دخول كمدير
2. جرب عرض إحصائيات النظام
3. جرب إدارة المستخدمين
4. جرب عرض المشاكل

## الإعداد للإنتاج

### 1. متطلبات الخادم
- خادم ويب (Apache/Nginx)
- PHP 7.4+ مع extensions مطلوبة
- MySQL 5.7+
- SSL certificate للأمان

### 2. إعدادات الأمان
```php
// في database.php للإنتاج
private $host = "your-db-host";
private $db_name = "progres_webtu";
private $username = "secure-username";
private $password = "strong-password";
```

### 3. تحسينات الأداء
- تفعيل caching
- ضغط الملفات
- تحسين قاعدة البيانات
- استخدام CDN للملفات الثابتة

### 4. النسخ الاحتياطي
- إعداد نسخ احتياطية دورية لقاعدة البيانات
- نسخ احتياطية للملفات
- مراقبة النظام

## الدعم والمساعدة

### موارد مفيدة
- [دليل PHP](https://www.php.net/manual/en/)
- [دليل MySQL](https://dev.mysql.com/doc/)
- [دليل JavaScript](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

### الحصول على المساعدة
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md
3. ابحث في Issues على GitHub
4. أنشئ issue جديد إذا لم تجد الحل

## ملاحظات مهمة

### الأمان
- غير كلمات المرور الافتراضية في الإنتاج
- استخدم HTTPS دائماً في الإنتاج
- طبق تشفير كلمات المرور
- راجع صلاحيات قاعدة البيانات

### الأداء
- راقب استخدام الذاكرة
- حسن استعلامات قاعدة البيانات
- استخدم فهرسة مناسبة
- طبق caching عند الحاجة

### الصيانة
- راجع logs بانتظام
- حدث النظام دورياً
- اختبر النسخ الاحتياطية
- راقب أداء الخادم
